import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Files, 
  Search, 
  GitBranch, 
  Package, 
  Settings, 
  User,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { ExplorerView } from '../explorer/ExplorerView';

interface SidebarLeftProps {
  isOpen: boolean;
  onFileSelect: (file: string) => void;
}

export function SidebarLeft({ isOpen, onFileSelect }: SidebarLeftProps) {
  const [activeTab, setActiveTab] = useState('explorer');
  const [expandedSections, setExpandedSections] = useState<string[]>(['explorer']);

  const tabs = [
    { id: 'explorer', icon: Files, label: 'Explorer', shortcut: 'Ctrl+Shift+E' },
    { id: 'search', icon: Search, label: 'Search', shortcut: 'Ctrl+Shift+F' },
    { id: 'git', icon: GitBranch, label: 'Source Control', shortcut: 'Ctrl+Shift+G' },
    { id: 'extensions', icon: Package, label: 'Extensions', shortcut: 'Ctrl+Shift+X' },
    { id: 'settings', icon: Settings, label: 'Settings', shortcut: 'Ctrl+,' },
    { id: 'profile', icon: User, label: 'Profile', shortcut: '' },
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="flex h-full bg-background">
      {/* Tab Icons */}
      <div className="w-12 bg-secondary border-r border-border flex flex-col">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`p-3 hover:bg-accent hover:text-accent-foreground transition-colors relative group ${
              activeTab === tab.id ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'
            }`}
            title={`${tab.label} ${tab.shortcut}`}
          >
            <tab.icon size={20} />
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeTab"
                className="absolute left-0 top-0 bottom-0 w-0.5 bg-primary"
                initial={false}
                transition={{ duration: 0.2 }}
              />
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 flex flex-col">
        {/* Tab Header */}
        <div className="h-8 flex items-center px-3 bg-secondary border-b border-border">
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            {tabs.find(tab => tab.id === activeTab)?.label}
          </span>
        </div>

        {/* Tab Content Area */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'explorer' && (
            <div className="h-full">
              {/* Explorer Header */}
              <div className="p-2 border-b border-border">
                <button
                  onClick={() => toggleSection('explorer')}
                  className="flex items-center w-full text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded px-2 py-1 transition-colors"
                >
                  {expandedSections.includes('explorer') ? (
                    <ChevronDown size={16} className="mr-1" />
                  ) : (
                    <ChevronRight size={16} className="mr-1" />
                  )}
                  EXPLORER
                </button>
              </div>

              {/* Explorer Content */}
              {expandedSections.includes('explorer') && (
                <div className="flex-1 overflow-auto">
                  <ExplorerView onFileSelect={onFileSelect} />
                </div>
              )}
            </div>
          )}

          {activeTab === 'search' && (
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    placeholder="Search files..."
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Replace..."
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  No results found
                </div>
              </div>
            </div>
          )}

          {activeTab === 'git' && (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                <div className="mb-4">
                  <h3 className="font-medium mb-2">Source Control</h3>
                  <p>Initialize repository to start tracking changes</p>
                </div>
                <button className="px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors">
                  Initialize Repository
                </button>
              </div>
            </div>
          )}

          {activeTab === 'extensions' && (
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    placeholder="Search extensions..."
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  <h3 className="font-medium mb-2">Popular Extensions</h3>
                  <div className="space-y-2">
                    <div className="p-2 border border-border rounded">
                      <div className="font-medium">KodeKilat Theme</div>
                      <div className="text-xs text-muted-foreground">Official theme pack</div>
                    </div>
                    <div className="p-2 border border-border rounded">
                      <div className="font-medium">Prettier</div>
                      <div className="text-xs text-muted-foreground">Code formatter</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-4">
              <div className="text-sm">
                <h3 className="font-medium mb-4">Settings</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">Theme</label>
                    <select className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm">
                      <option>Dark (Default)</option>
                      <option>Light</option>
                      <option>Auto</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Font Size</label>
                    <input
                      type="number"
                      defaultValue={14}
                      className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="p-4">
              <div className="text-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <User size={20} className="text-primary-foreground" />
                  </div>
                  <div>
                    <div className="font-medium">Developer</div>
                    <div className="text-xs text-muted-foreground">Local User</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Sync Settings
                  </button>
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Backup Data
                  </button>
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
