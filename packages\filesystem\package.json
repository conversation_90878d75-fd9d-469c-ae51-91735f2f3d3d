{"name": "@kodekilat/filesystem", "version": "1.0.0", "description": "KodeKilat Studio - File System Components", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "typescript": "^5.3.0", "rimraf": "^5.0.5"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}