import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Menu, 
  Search, 
  Command, 
  Minimize2, 
  Maximize2, 
  X,
  ChevronDown,
  Folder,
  Settings
} from 'lucide-react';
import { FileMenu } from '../menu/FileMenu';
import { EditMenu } from '../menu/EditMenu';
import { ViewMenu } from '../menu/ViewMenu';

interface HeaderProps {
  onToggleLeftSidebar: () => void;
  onToggleRightSidebar: () => void;
  leftSidebarOpen: boolean;
  rightSidebarOpen: boolean;
}

export function Header({ 
  onToggleLeftSidebar, 
  onToggleRightSidebar, 
  leftSidebarOpen, 
  rightSidebarOpen 
}: HeaderProps) {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [projectName, setProjectName] = useState('KodeKilat Studio');

  const menuItems = [
    { id: 'file', label: 'File', component: FileMenu },
    { id: 'edit', label: 'Edit', component: EditMenu },
    { id: 'view', label: 'View', component: ViewMenu },
    { id: 'go', label: 'Go', component: null },
    { id: 'run', label: 'Run', component: null },
    { id: 'terminal', label: 'Terminal', component: null },
    { id: 'help', label: 'Help', component: null },
  ];

  const handleMenuClick = (menuId: string) => {
    setActiveMenu(activeMenu === menuId ? null : menuId);
  };

  const handleWindowControl = (action: 'minimize' | 'maximize' | 'close') => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      window.electronAPI.windowControl(action);
    }
  };

  return (
    <header className="flex items-center h-12 bg-background border-b border-border px-2 select-none">
      {/* Left Section - Logo & Menu */}
      <div className="flex items-center space-x-1">
        {/* Logo */}
        <div className="flex items-center space-x-2 px-2">
          <svg width="20" height="20" viewBox="0 0 80 80" className="text-primary">
            <path
              d="M30 20 L50 35 L45 35 L60 60 L40 45 L45 45 Z"
              fill="currentColor"
              className="drop-shadow-sm"
            />
          </svg>
          <span className="text-sm font-semibold text-primary">KodeKilat Studio</span>
        </div>

        {/* Menu Items */}
        <div className="flex items-center">
          {menuItems.map((item) => (
            <div key={item.id} className="relative">
              <button
                onClick={() => handleMenuClick(item.id)}
                className={`px-3 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors ${
                  activeMenu === item.id ? 'bg-accent text-accent-foreground' : ''
                }`}
              >
                {item.label}
              </button>
              
              {/* Dropdown Menu */}
              {activeMenu === item.id && item.component && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 z-50 mt-1"
                >
                  <item.component onClose={() => setActiveMenu(null)} />
                </motion.div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Center Section - Project Name & Breadcrumb */}
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Folder size={16} />
          <span>{projectName}</span>
          <ChevronDown size={14} />
        </div>
      </div>

      {/* Right Section - Controls */}
      <div className="flex items-center space-x-1">
        {/* Search */}
        <button className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors">
          <Search size={16} />
        </button>

        {/* Command Palette */}
        <button 
          className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
          title="Command Palette (Ctrl+Shift+P)"
        >
          <Command size={16} />
        </button>

        {/* Settings */}
        <button className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors">
          <Settings size={16} />
        </button>

        {/* Window Controls */}
        <div className="flex items-center ml-2 space-x-1">
          <button
            onClick={() => handleWindowControl('minimize')}
            className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
          >
            <Minimize2 size={14} />
          </button>
          <button
            onClick={() => handleWindowControl('maximize')}
            className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
          >
            <Maximize2 size={14} />
          </button>
          <button
            onClick={() => handleWindowControl('close')}
            className="p-2 hover:bg-destructive hover:text-destructive-foreground rounded-sm transition-colors"
          >
            <X size={14} />
          </button>
        </div>
      </div>
    </header>
  );
}
