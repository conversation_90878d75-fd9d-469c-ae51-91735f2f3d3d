{"name": "@kodekilat/editor", "version": "1.0.0", "description": "KodeKilat Studio - Monaco Editor Wrapper", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.44.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "typescript": "^5.3.0", "rimraf": "^5.0.5"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}